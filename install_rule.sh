#!/bin/bash
# install_rule.sh - Selecteer en kopieer een .cursorrules-bestand naar je eigen project

set -e

RULES_DIR="$(dirname "$0")/rules"

if [ ! -d "$RULES_DIR" ]; then
  echo "Rules directory niet gevonden: $RULES_DIR"
  exit 1
fi

echo "\nBeschikbare rule-categorieën:" >&2
select CATEGORY in "$RULES_DIR"/*/; do
  [ -n "$CATEGORY" ] && break
  echo "Ongeldige keuze, probeer opnieuw." >&2
done

shopt -s nullglob
RULE_FILES=("$CATEGORY"*.mdc "$CATEGORY"*.md)
shopt -u nullglob

if [ ${#RULE_FILES[@]} -eq 0 ]; then
  echo "Geen .mdc of .md bestanden gevonden in $CATEGORY" >&2
  exit 1
fi

echo "\nBeschikbare rule-bestanden in $CATEGORY:" >&2
select RULE_FILE in "${RULE_FILES[@]}"; do
  [ -n "$RULE_FILE" ] && break
  echo "Ongeldige keuze, probeer opnieuw." >&2
done

read -rp $'\nGeef het volledige pad op naar de root van je eigen project (waar .cursorrules moet komen):\n> ' TARGET_DIR

if [ ! -d "$TARGET_DIR" ]; then
  echo "Map bestaat niet: $TARGET_DIR" >&2
  exit 1
fi

cp "$RULE_FILE" "$TARGET_DIR/.cursorrules"
echo "\nKlaar! $RULE_FILE gekopieerd naar $TARGET_DIR/.cursorrules" 