# Code Guidelines .cursorrules prompt file

Author: <PERSON><PERSON>

## What you can build
Code Analysis Tool: Develop a tool that analyzes code for compliance with the rules set in the .cursorrules AI file. It would highlight violations such as missing error handling, lack of test coverage, and unmatched coding style.Automated Refactoring Service: Create a service that automatically refactors code file-by-file while adhering to guidelines like no whitespace suggestions, avoiding magic numbers, and ensuring modular design.Project Consistency Checker: Build a tool that checks for consistent coding style, performance prioritization, and security considerations across multiple files in a project, while avoiding unnecessary updates.Developer Assistant Extension: Design a browser or IDE extension that provides real-time feedback on code edits, emphasizing explicit variable names, avoiding apologies, and ensuring no unnecessary confirmations are needed.Code Optimization Platform: Set up a platform that suggests performance improvements and modular design strategies while ensuring version compatibility and prioritizing a security-first approach.Unit Test Suggestions App: Create an app that automatically generates unit tests for new or modified code, emphasizing coverage, without showing or discussing the current implementation unless requested.Version Compatibility Analyzer: Develop an analyzer that checks code changes for compatibility with the project's specified language or framework versions to ensure smooth integration and functionality.Edge Case Identifier: Build a tool that scans code to identify potential edge cases and suggests handling strategies, ensuring robust error handling and logging.Context-Aware Code Reviewer: Create a code review platform that uses the context generated files to provide feedback on edits, ensuring no unnecessary confirmations and file preservation.Modular Design Educator: Develop a learning platform that educates developers on implementing and encouraging modular design principles, focusing on reusability and maintainability of code.Assertion Validator: Construct a service that integrates into CI/CD pipelines to check the presence and correct use of assertions in code, enhancing validation accuracy and early error detection.Hardcoded Values Detector: Create a tool that scans codebases for magic numbers and suggests replacements with named constants, improving clarity and maintainability.Error Handling Enhancer: Design a plugin or standalone tool that suggests improvements in error handling mechanisms within code, providing recommendations for robust logging practices.

## Benefits


## Synopsis
Developers working on collaborative projects would benefit by establishing clear and efficient code review and update practices, ensuring consistent, secure, and maintainable code quality across the team.

## Overview of .cursorrules prompt
The .cursorrules file outlines a set of rules and guidelines to be followed when editing or suggesting changes to code. It emphasizes verifying information, making changes file-by-file, preserving existing code, and avoiding unnecessary confirmations or updates. It advises against using apologies, unnecessary whitespace changes, or summarizing changes made. There is a focus on ensuring real file links are provided, using explicit variable names, and following a consistent coding style. Performance, security, error handling, modular design, version compatibility, edge cases, and test coverage are prioritized. The file discourages the use of "magic numbers" and encourages using assertions to catch errors early.

