---
description: Provides performance-related guidelines for Flutter development, including image caching, list view optimization, and memory management.
globs: lib/**/*.*
---
- Use proper image caching.
- Implement proper list view optimization.
- Use proper build methods optimization.
- Follow proper state management patterns.
- Implement proper memory management.
- Use proper platform channels when needed.
- Follow proper compilation optimization techniques.