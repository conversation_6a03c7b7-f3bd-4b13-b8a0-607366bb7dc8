---
description: Enforces clean architecture, BLoC pattern, and state management principles within Flutter feature modules.
globs: lib/features/**/*.*
---
- Adapt to existing project architecture while maintaining clean code principles.
- Use Flutter 3.x features and Material 3 design.
- Implement clean architecture with BLoC pattern.
- Follow proper state management principles.
- Use proper dependency injection.
- Implement proper error handling.
- Follow proper state management with BLoC.
- Implement proper dependency injection using GetIt.