---
description: Applies general Flutter best practices across the entire project, focusing on architecture, design, and code quality.
globs: lib/**/*.*
---
- Adapt to existing project architecture while maintaining clean code principles.
- Use Flutter 3.x features and Material 3 design.
- Implement clean architecture with BLoC pattern.
- Follow proper state management principles.
- Use proper dependency injection.
- Implement proper error handling.
- Follow platform-specific design guidelines.
- Use proper localization techniques.