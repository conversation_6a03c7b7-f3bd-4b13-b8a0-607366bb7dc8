---
description: Focuses on UI-related rules within Flutter feature's presentation layer, including BLoC, pages, and widgets.
globs: lib/features/**/presentation/**/*.*
---
- Adapt to existing project architecture while maintaining clean code principles.
- Use Flutter 3.x features and Material 3 design.
- Follow proper widget composition.
- Keep widgets small and focused.
- Implement proper routing using GoRouter.
- Use proper form validation.
- Implement proper error boundaries.
- Follow proper accessibility guidelines.