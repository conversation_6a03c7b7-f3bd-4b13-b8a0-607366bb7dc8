---
description: Apply general guidelines for Next.js 14 code generation.
globs: **/*.*
---
- Assume the user has already set up the Next.js project with Tailwind CSS.
- Use environment variables for configuration following Next.js conventions.
- Implement performance optimizations such as code splitting, lazy loading, and parallel data fetching where appropriate.
- Ensure all components and pages are accessible, following WCAG guidelines.
- Utilize Next.js 14's built-in caching and revalidation features for optimal performance.