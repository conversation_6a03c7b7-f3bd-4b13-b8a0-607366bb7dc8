you are an expert game designer and game programmer, you will choose the best game design and coding practices for all decisions in this project.

The game is based on a 10x10 grid, each square has a 10x10 grid inside of it. There must be random map generation that smartly calculates where resources are located and how the map is generated.

The player does not control anything in the game the player is simply an observer, therefore there should be logs for almost everything in the game and it should be turn based.

All nations should operate the same, their capabilities should be balanced. The player should be able to see the entire map at once, and the player should be able to see the entire history of the game in the logs. There should be a way to zoom in on a specific square to see more detail.

Nations should be able to trade resources with each other. Nations should be able to go to war with each other. Nations should be able to make peace with each other.

The time period of the game is constant and there is no technological tree. It takes place in ancient times.

nations should spawn a minimum distance away from eachother

the entire game should be colored ASCII based in terms of graphics

There should be neutral land that can be claimed by any nation. Neutral land should be randomly generated each game.

There should be a way to view the current owner of a square. There should be a way to view the current resources of a square.

value of resources should be based on their rarity throughout the entire map. nations can use gold to either buy resources or armies.

armies are the primary way that nations can expand their territory.

there should be no talent tree or technology tree, nations should be balanced without the need for such a tree

population should collect in towns and cities

roads should connect towns and cities

resources are spread throughout nations through roads

nations attempt to spread their resources evenly over their territory

gold is not omni present and must be transported using roads to the location where it is spent to build armies or develop land

oceans should be randomly generated to separate continents

rivers should be randomly generated to connect oceans and flow across the map vertically or horizontally

rivers are a food source for the land and farms can be built on them

mountains should be randomly generated throughout the map

mountains should be impassable by armies

mines in mountains provide metal at 20% efficiency

Nations should expand towards resources that they have a low amount of of and away from resources that they have a high amount of

armies should spawn at the town or city that issued the order

towns can only spawn a max level 3 army

towns have a 3 square radius for gathering resources

as towns grow their radius grows, there are 3 levels of towns and cities

a Nation's largest city is its capital

population can only live in towns and cities

resources should be spread throughout the map in a way that encourages nations to expand into new squares

armies can travel across oceans at .25x speed

armies can travel on rivers to move across the map at 3x speed

there is a "battle list" that shows all the battles that have happened and stats about them

armies go from level 1 to level 10 based on their funding

inner squares can be developed into farms, forests, mines

armies require wood, food, and metal to be created.

nations must pay upkeep depending on the amount of armies and developed land they have

battles are resolved by the difference in army level and a RISK esque dice roll mechanic that is effected by army level

armies can build castles that are good defensively and allow for funding of armies

armies can be used to conquer squares from other nations

armies can be used to defend squares from other nations

armies can be used to attack other nations

armies can be used to attack neutral squares

armies can be used to attack other nations squares

armies can be used to attack neutral squares

armies can be used to attack other nations squares

armies can be used to attack neutral squares

nations should start with the same amount of gold and land

the map should be color coded to show the owner of the square

there should be effects over the screen that mimic a CRT monitor

the game should aim to be similar to Conway's Game of Life where the nations are the living organisms.

like conway's game of life, nations should be able to "see" eachother and react to eachother

like conway's game of life, the nations should be able to "see" the resources and react to them

there should be a chart page that tracks just about everything that can be tracked in the game

