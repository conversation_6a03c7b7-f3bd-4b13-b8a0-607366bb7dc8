# ASCII Simulation Game .cursorrules prompt file

Author: haldave159

## What you can build
Resource Expansion Simulator: Create a web-based game where users can observe and analyze how nations expand based on resource availability. This app would allow users to set parameters for the random map generation and then watch as the nations compete to control resources and territory.Historical Strategy Observer: Develop an interactive application that simulates ancient civilizations' expansion, trading, and warfare. Users can zoom in on specific areas of the grid to observe resource management, battles, and nation interactions in detail.ASCII Map Visualizer: Build a tool that visualizes the colored ASCII-based graphics of the game's map, allowing users to see the current owner and resources of each square, thus aiding in teaching game development concepts using ASCII art.Conway-Style AI Nation Simulator: Create a simulation that uses AI to mimic ancient civilizations' decision-making processes, inspired by <PERSON>'s Game of Life. Users observe nations creating armies, trading resources, and expanding territories.Nation Battle Analyzer: Design a platform that collects and displays the battle history from the game, helping users study the effects of army level, resource allocation, and dice roll mechanics on battles through detailed logs.Supply Chain Dynamics Game: Implement a game focusing on the transportation of gold and resources through roads, helping users understand logistics and supply chain management in an ancient context.Economic Balance Evaluator: Develop a tool that simulates resource valuation and trade dynamics in ancient civilizations, providing insights into how balanced resource distribution impacts nation growth.Strategic Map Explorer: Create an app that offers detailed insights into how neutral lands can be claimed, focusing on the strategic importance of various terrains (rivers, mountains) in expansion strategies.Historical Growth Charts: Produce comprehensive charts and graphs that track every aspect of nation development throughout the game, offering a detailed historical overview for educational purposes.ASCII Strategy Game Framework: Offer a framework for developers to create similar ASCII-based strategy games, providing code examples and guidance on best practices for creating complex grid-based simulations.

## Benefits


## Synopsis
This prompt would benefit simulation game developers, allowing them to create a procedurally generated, observer-based strategy game with ASCII graphics and detailed logging of nation interactions.

## Overview of .cursorrules prompt
The .cursorrules file describes a complex simulation game set in ancient times where the player is an observer rather than an active participant. This simulation is rendered in ASCII graphics on a 10x10 grid with sub-grids and features random map generation with balanced capabilities for nations. Nations can trade, go to war, and make peace while expanding their territories through strategic resource management and army deployment. Key mechanics include resource rarity, turn-based decisions, and a CRT monitor-like display style. The game mirrors concepts from Conway's Game of Life, emphasizing autonomous interactions between nations and resources. Detailed logging and a trackable history of gameplay are emphasized for comprehensive insight into game dynamics.

