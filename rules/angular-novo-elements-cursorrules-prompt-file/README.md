# Angular Novo Elements .cursorrules prompt file

Author: <PERSON>

## What you can build
Code Refactoring Tool: Develop an intelligent code refactoring tool that adheres to the principles outlined in the .cursorrules file. This tool can automatically detect code smells, suggest improvements for code simplification, and ensure best practices like DRY, KISS, and the Boy-Scout Rule are applied.Unit Test Generator: Create a tool that automatically generates unit tests for Angular components, ensuring all new code is thoroughly tested before merging. The tool should analyze existing code and create tests that cover a wide range of edge cases.Real-Time Code Review System: Build a platform that provides real-time feedback during code writing, highlighting areas where the principles like YAGNI, <PERSON><PERSON>’s Law, and the Principle of Least Astonishment can be applied to improve code quality.Angular Component Analyzer: Develop a service that analyzes standalone Angular components and provides insights into their integration with Novo Elements. It should check for best practices, compatibility issues, and optimization opportunities.Code Maintenance Dashboard: Construct a dashboard for tracking the maintainability of a codebase. It should use metrics to evaluate cleanliness, adherence to rules like DRY and <PERSON><PERSON>’s Law, and offer suggestions for improvement, making code maintenance easier.Project Structure Optimizer: Design a tool that suggests optimal file and directory structures for Angular projects using Novo Elements, ensuring the project remains clear, organized, and free from clutter.Automated Documentation Generator: Develop a generator that creates comprehensive and easy-to-understand documentation for Angular projects, focusing on clarifying code functionality and expected behavior to follow the Principle of Least Astonishment.Debugging Assistant: Create an AI-powered debugging assistant that helps identify and resolve issues by generating detailed logs and providing potential fixes, based on the debugging and testing rules from .cursorrules.Code Quality Checker: Implement a service that checks code against the provided style and formatting guidelines in real-time, ensuring consistent naming conventions and the use of up-to-date libraries.Premature Optimization Detector: Develop a tool that analyzes code for unnecessary optimizations and advises developers on when to focus on critical efficiencies, in line with the concept that premature optimization is the root of all evil.

## Benefits


## Synopsis
This prompt is useful for Angular developers working with standalone components and Novo Elements, providing guidelines to ensure efficient, maintainable code that adheres to modern best practices.

## Overview of .cursorrules prompt
The .cursorrules file outlines a set of coding standards and principles to guide developers in creating optimal, production-ready code. It emphasizes preserving existing code structures, verifying changes, and delivering concise solutions. Key coding principles such as DRY (Don't Repeat Yourself), KISS (Keep It Simple Stupid), and YAGNI (You Aren't Gonna Need It) are highlighted to maintain code quality and avoid unnecessary complexity. It also includes specific rules for debugging, testing, and maintaining project structure, while integrating Angular with standalone components and Novo Elements. The file aims for thorough testing, clear documentation, and minimal diffs in code changes to meet specified project requirements efficiently.

