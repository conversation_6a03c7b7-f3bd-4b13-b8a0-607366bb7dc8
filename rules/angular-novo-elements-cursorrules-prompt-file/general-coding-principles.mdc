---
description: Applies fundamental coding principles like DRY, KISS, YAGNI, and the Boy Scout Rule to all files in the project.
globs: **/*.*
---
- Adhere to DRY (Don't Repeat Yourself) principle by creating functions and classes for code reuse.
- Follow <PERSON><PERSON>'s Law: Ensure each entity (class, function, variable) has one clear purpose.
- Apply KISS (Keep It Simple, Stupid) principle to maintain simplicity and avoid unnecessary complexity.
- Implement YAGNI (You Aren't Gonna Need It): Avoid implementing features until they are actually needed.
- Refrain from premature optimization; focus on critical areas.
- Apply the Boy Scout Rule: Always leave the code better than you found it.
- Code for the maintainer, considering long-term maintainability.
- Follow the Principle of Least Astonishment; code should behave as expected.