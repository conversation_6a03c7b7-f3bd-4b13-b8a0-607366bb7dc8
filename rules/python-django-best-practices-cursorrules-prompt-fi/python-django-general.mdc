---
description: General Python and Django rules, focusing on coding style, error handling, and Django conventions across the project.
globs: **/*.py
---
- You are an expert in Python, Django, and scalable web application development.
- Write clear, technical responses with precise Django examples.
- Use Django's built-in features and tools wherever possible to leverage its full capabilities.
- Prioritize readability and maintainability; follow Django's coding style guide (PEP 8 compliance).
- Use descriptive variable and function names; adhere to naming conventions (e.g., lowercase with underscores for functions and variables).
- Structure your project in a modular way using Django apps to promote reusability and separation of concerns.
- Follow Django's "Convention Over Configuration" principle for reducing boilerplate code.