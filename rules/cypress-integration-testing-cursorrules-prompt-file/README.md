# Cypress Integration Testing Prompt

A specialized .cursorrules prompt for creating comprehensive integration tests using Cypress with TypeScript support.

## What You Can Build

- **Integration Test Suites**: Tests that verify interactions between UI and API components
- **Critical User Flow Tests**: Tests for essential user journeys across multiple components
- **API Mock-Based Testing**: Integration tests with controlled API response scenarios
- **State Transition Tests**: Validations of application state changes during component interactions
- **Cross-Component Tests**: Tests that verify data flows between connected components

## Benefits

- **Complete Component Interaction Coverage**: Tests that verify how components work together
- **API Dependency Isolation**: Control over API responses using cy.intercept for reliable testing
- **Realistic User Journey Testing**: Focus on critical flows that users actually experience
- **Proper State Validation**: Verification that UI state updates correctly based on API responses
- **Error Path Coverage**: Testing of both happy paths and error scenarios
- **Maintainable Test Organization**: Descriptive test structure that documents component integration

## Synopsis

This prompt helps QA engineers create high-quality integration tests with Cypress that focus on how UI components interact with APIs and each other, ensuring critical user flows work correctly across the application.

## Overview of .cursorrules Prompt

The .cursorrules prompt guides QA engineers in creating effective integration tests using Cypress with these key elements:

- **TypeScript Detection**: Automatically detects and adapts to TypeScript usage in the project
- **Integration Testing Focus**: Guidelines for testing component interactions and critical user flows
- **Best Practices**: Eight essential practices for integration testing, including critical flows, data-testid usage, and API mocking
- **Example Test Patterns**: Detailed examples of integration tests for both form submission and shopping cart scenarios
- **API Mocking Strategy**: Approach for using cy.intercept to control API responses during integration tests
- **State Validation**: Methods for verifying UI state updates correctly based on API interactions
- **Error Handling Testing**: Techniques for testing both success and error paths
