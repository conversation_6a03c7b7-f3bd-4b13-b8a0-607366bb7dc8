# .cursorrules file Cursor AI Python FastAPI API

Author: <PERSON><PERSON><PERSON>

## What you can build
API Performance Monitoring Tool: A web app that uses FastAPI to track, analyze, and optimize API performance metrics such as response time, latency, and throughput. It will provide real-time dashboards and alerts for performance issues.Async API Wrapper Generator: A command-line tool that generates FastAPI-based Python code for interfacing with external APIs. It will automatically include async functions for non-blocking API operations and error-handling patterns.Validation and Error Handling Library: A Python library that provides utilities and decorators for consistent error handling and input validation using Pydantic in FastAPI projects. It will focus on guard clauses, custom error types, and error logging.Database Interaction Utility: A lightweight Python package that facilitates the use of async database libraries with SQLAlchemy 2.0 in FastAPI, focusing on optimizing query performance and using lazy loading techniques.FastAPI Middleware Suite: A collection of pre-built middleware for FastAPI applications focusing on logging, error monitoring, performance optimization, and security enhancements.Scalable API Bootstrapping Service: A web-based service that allows users to generate boilerplate code for scalable FastAPI applications, adhering to best practices in API development, modular file structures, and dependency injection patterns.Pydantic Schema Generator: A GUI application that generates Pydantic models and schemas from JSON or YAML files, aiding in the consistent use of input/output validation and response schemas in FastAPI projects.Cache Management Plugin: A FastAPI plugin that facilitates the integration and management of caching strategies using tools like Redis for optimizing the performance of frequently accessed endpoints.Async Workflow Orchestrator: A tool for managing complex async workflows and I/O-bound tasks in FastAPI applications, providing templates and patterns for building robust and non-blocking routes.FastAPI Route Optimizer: An IDE plugin or script that reviews FastAPI code to suggest optimizations for route definitions, dependency injection usage, and async operation patterns to enhance readability and performance.

## Benefits


## Synopsis


## Overview of .cursorrules prompt
The .cursorrules file outlines key principles and guidelines for developing scalable APIs using Python and FastAPI. It emphasizes writing concise and technical responses with accurate code examples, adhering to functional programming principles, and employing modular and iterative approaches to reduce code duplication. The file provides detailed instructions on Python/FastAPI usage, including the structure of files and functions, error handling, and dependency requirements. It highlights performance optimization tactics such as using asynchronous operations, caching, and lazy loading. Key conventions include the reliance on FastAPI's dependency injection system, focusing on API performance metrics, and limiting blocking operations. It encourages adherence to FastAPI's best practices for data models, path operations, and middleware.

