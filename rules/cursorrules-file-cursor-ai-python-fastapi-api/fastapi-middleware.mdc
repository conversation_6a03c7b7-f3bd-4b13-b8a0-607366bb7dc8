---
description: Specifies the use of middleware for logging, error monitoring, and performance optimization in FastAPI applications.
globs: **/middleware/*.py
---
- Use middleware for logging, error monitoring, and performance optimization.
- Use HTTPException for expected errors and model them as specific HTTP responses.
- Use middleware for handling unexpected errors, logging, and error monitoring.
- Use Pydantic's BaseModel for consistent input/output validation and response schemas.