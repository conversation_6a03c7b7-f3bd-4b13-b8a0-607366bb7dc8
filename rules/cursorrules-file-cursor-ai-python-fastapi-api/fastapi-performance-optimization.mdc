---
description: Outlines performance optimization techniques for FastAPI applications, including asynchronous operations and caching.
globs: **/*.py
---
- Minimize blocking I/O operations; use asynchronous operations for all database calls and external API requests.
- Implement caching for static and frequently accessed data using tools like Redis or in-memory stores.
- Optimize data serialization and deserialization with Pydantic.
- Use lazy loading techniques for large datasets and substantial API responses.