---
description: Provides additional guidelines for HTMX development, focusing on semantic HTML, security, extensions, progressive enhancement, and server-side templating.
globs: **/*.html
---
- Use semantic HTML5 elements
- Implement proper CSRF protection
- Utilize HTMX extensions when needed
- Use hx-boost for full page navigation
- Implement proper error handling
- Follow progressive enhancement principles
- Use server-side templating (e.g., Jinja2, Handlebars)