# Engineering Ticket Template Prompt

A specialized .cursorrules prompt for creating standardized engineering tickets with detailed requirements, implementation plans, and acceptance criteria for effective development team collaboration.

## What You Can Build

- **Structured Tickets**: Standardized, comprehensive engineering tickets for any task management system
- **Implementation Roadmaps**: Clear, step-by-step guides for feature implementation
- **Acceptance Criteria**: Well-defined success criteria in list or BDD formats
- **Technical Specifications**: Detailed technical contexts and constraints
- **Sprint Planning Aids**: Story point estimations and sprint assignment guidance

## Benefits

- **Clear Requirements**: Structured format that clearly communicates what needs to be built
- **Technical Context**: Background information that helps engineers understand the task
- **Implementation Guidance**: Suggestions without over-prescribing solutions
- **Sprint Planning**: Effort estimates to support agile planning processes
- **Cross-Team Alignment**: Format that bridges product, engineering, and QA perspectives
- **Reduced Ambiguity**: Comprehensive template that minimizes clarification questions

## Synopsis

This prompt helps technical product managers create standardized, comprehensive engineering tickets that provide developers with all the information needed to understand, implement, and test new features efficiently.

## Overview of .cursorrules Prompt

The .cursorrules prompt guides users in creating effective engineering tickets with these key elements:

- **Flexible Formats**: Support for both list-style and Given-When-Then acceptance criteria
- **Comprehensive Sections**: Complete template with all essential engineering ticket components
- **Detailed Examples**: Two comprehensive examples using different formats
- **Best Practices**: Ten key principles for writing effective engineering tickets
- **Adaptability Guidance**: Advice for customizing tickets for different tools and teams
- **Balance**: Focus on providing sufficient detail while allowing technical creativity
