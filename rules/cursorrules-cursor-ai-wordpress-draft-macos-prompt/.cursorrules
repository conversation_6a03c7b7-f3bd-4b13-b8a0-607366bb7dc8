This project is called PressThat.

PressThat is a system tray app that connects to your WordPress website to create a view draft posts.

After first installing the app, you need to configure it with your website details. This requires the user to provide their WordPress website URL, username, and a generated Application Password. 

Users can generate an Application Password in their WordPress dashboard at the bottom of the "Users -> Profile" page. This password is unique and can be easily revoked at any time.

Here's a quick flow for how the new user experience (NUX) will work:

