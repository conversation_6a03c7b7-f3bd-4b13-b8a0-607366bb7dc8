---
description: This rule dictates how comments should be used within the codebase to enhance understanding and avoid clutter.
globs: **/*.*
---
- Use comments sparingly, and when you do, make them meaningful.
- Don't comment on obvious things. Excessive or unclear comments can clutter the codebase and become outdated.
- Use comments to convey the "why" behind specific actions or explain unusual behavior and potential pitfalls.
- Provide meaningful information about the function's behavior and explain unusual behavior and potential pitfalls.