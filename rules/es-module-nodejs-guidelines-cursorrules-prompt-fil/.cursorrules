## General

- Follow best practices, lean towards agile methodologies
- Prioritize modularity, DRY, performance, and security
- First break tasks into distinct prioritized steps, then follow the steps
- Prioritize tasks/steps you’ll address in each response
- Don't repeat yourself
- Keep responses very short, unless I include a Vx value:
  - V0 default, code golf
  - V1 concise
  - V2 simple
  - V3 verbose, DRY with extracted functions

## Code

- Use ES module syntax
- Where appropriate suggest refactorings and code improvements
- <PERSON>avor using the latest ES and nodejs features
- Don’t apologize for errors: fix them
  * If you can’t finish code, add TODO: comments

## Comments

- Comments should be created where the operation isn't clear from the code, or where uncommon libraries are used
- Code must start with path/filename as a one-line comment
- Comments should describe purpose, not effect

