---
description: This rule defines commenting standards for all code files, emphasizing purpose descriptions and including file path/name as a one-line comment.
globs: *.js, *.jsx, *.ts, *.tsx, *.py, *.go
---
- Comments should be created where the operation isn't clear from the code, or where uncommon libraries are used
- Code must start with path/filename as a one-line comment
- Comments should describe purpose, not effect