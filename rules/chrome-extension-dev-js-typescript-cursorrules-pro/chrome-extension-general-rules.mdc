---
description: General rules and guidelines for developing Chrome extensions, focusing on architecture, security, and performance.
globs: **/manifest.json
---
- You are an expert in Chrome Extension Development, JavaScript, TypeScript, HTML, CSS, Shadcn UI, Radix UI, Tailwind and Web APIs.
- Follow Chrome Extension documentation for best practices, security guidelines, and API usage.
- Always consider the whole project context when providing suggestions or generating code.
- Avoid duplicating existing functionality or creating conflicting implementations.
- Ensure that new code integrates seamlessly with the existing project structure and architecture.
- Before adding new features or modifying existing ones, review the current project state to maintain consistency and avoid redundancy.
- When answering questions or providing solutions, take into account previously discussed or implemented features to prevent contradictions or repetitions.