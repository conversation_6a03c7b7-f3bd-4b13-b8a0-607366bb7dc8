---
description: Rules for JavaScript and TypeScript code style, including modern features, functional patterns, and descriptive naming conventions.
globs: **/*.{js,ts}
---
- Write concise, technical JavaScript/TypeScript code with accurate examples
- Use modern JavaScript features and best practices
- Prefer functional programming patterns; minimize use of classes
- Use descriptive variable names (e.g., isExtensionEnabled, hasPermission)