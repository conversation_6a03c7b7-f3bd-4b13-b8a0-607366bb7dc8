---
description: Provides guidelines for creating conventional commit messages, ensuring they adhere to a specific format and are concise.
globs: *
---
- Always suggest a conventional commit with a type and optional scope in lowercase letters.
- Keep the commit message concise and within 60 characters.
- Ensure the commit message is ready to be pasted into the terminal without further editing.
- Provide the full command to commit, not just the message.