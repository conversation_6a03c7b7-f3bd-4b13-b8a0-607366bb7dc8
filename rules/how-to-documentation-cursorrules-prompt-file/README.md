# How-To Documentation Prompt

A specialized .cursorrules prompt for creating clear, user-friendly "How To" documentation that helps non-technical users understand software features.

## What You Can Build

- **User Guides**: Step-by-step instructions for using application features
- **Training Materials**: Documentation for onboarding new users to your software
- **Knowledge Base Articles**: Searchable help content for common user questions
- **Support Documentation**: Troubleshooting guides and usage instructions
- **Platform-Specific Guides**: Instructions tailored to different devices or platforms

## Benefits

- **Accessible Documentation**: Technical concepts translated into simple, user-friendly language
- **Consistent Format**: Standardized structure that's easy to follow and understand
- **Improved User Experience**: Clear instructions that reduce support tickets and user frustration
- **Versatile Output Formats**: Content suitable for various documentation platforms (Google Docs, Microsoft Word, Atlassian, etc.)
- **Technical Translation**: Convert technical test scripts or developer notes into user-friendly guides
- **Troubleshooting Inclusion**: Common issues and solutions incorporated into documentation

## Synopsis

This prompt helps technical writers and developers create high-quality "How To" documentation that bridges the gap between technical functionality and user understanding, making software features accessible to all users.

## Overview of .cursorrules Prompt

The .cursorrules prompt guides technical writers in creating effective "How To" documentation with these key elements:

- **Documentation Focus**: Guidelines for creating step-by-step instructions in user-friendly language
- **Best Practices**: Eight essential practices for clear, effective documentation
- **Document Structure**: Standardized format with title, introduction, prerequisites, steps, and troubleshooting
- **Example Document**: Detailed example of a complete "How To" document in Markdown format
- **Technical Conversion**: Process for converting technical scripts or stories into user-friendly documentation
- **Simplified Language**: Emphasis on using non-technical terms and clear explanations
- **Visual References**: Techniques for referencing UI elements as they appear to users
