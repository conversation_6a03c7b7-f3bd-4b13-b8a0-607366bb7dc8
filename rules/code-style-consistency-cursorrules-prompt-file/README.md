# Code Style Consistency Prompt

Author: <PERSON>

A specialized .cursorrules prompt for analyzing codebase patterns and ensuring new AI-generated code follows the established style and conventions of the project.

## What You Can Build

- **Style Analysis Reports**: Comprehensive profiles of existing codebases detailing naming conventions, formatting, and architectural patterns
- **Consistent Feature Implementations**: New features that seamlessly integrate with existing code style
- **Adaptive Refactoring**: Updates to existing code that maintain stylistic integrity
- **Style Adaptation Middleware**: A processing layer that transforms arbitrary code to match project patterns
- **Architecture Pattern Detectors**: Tools that identify and catalog structural patterns in the codebase

## Benefits

- **Seamless Integration**: Generated code that blends naturally with existing project code
- **Team Alignment**: Ensures AI assistance matches team standards without additional rework
- **Reduced Cognitive Load**: Consistent code is easier to read, understand, and maintain
- **Improved Collaboration**: Minimizes style-based conflicts in code reviews and merges
- **Progressive Enhancement**: Adapts to the evolving patterns within a codebase
- **Context-Aware Assistance**: AI recommendations that respect project-specific conventions

## Synopsis

This prompt serves as a powerful prefix to any code generation task, analyzing a project's existing style patterns before generating new code that harmoniously integrates with established conventions.

## Overview of .cursorrules Prompt

The .cursorrules prompt guides developers in maintaining code style consistency with these key elements:

- **Style Analysis Framework**: Comprehensive methodology for examining and cataloging code patterns
- **Style Profile Template**: Structured format for documenting identified conventions
- **Practical Examples**: Demonstrations of code adaptation based on style analysis
- **Consistency Best Practices**: Ten key principles for maintaining stylistic integrity
- **File Analysis Strategy**: Guidelines for selecting representative files to establish patterns
- **Adaptation Techniques**: Specific methods for transforming code to match existing patterns
