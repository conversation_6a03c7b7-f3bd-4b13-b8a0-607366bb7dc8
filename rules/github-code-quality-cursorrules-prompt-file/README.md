# GitHub Code Quality .cursorrules prompt file

Author: meowso

## What you can build
Automated Code Review Tool: An app that automatically reviews code and flags potential issues based on the rules provided. It ensures that code adheres to best practices, such as avoiding assumptions, verifying information, and making precise file-by-file changes.Collaborative Coding Platform: A platform that enforces coding rules while enabling multiple developers to work on a project simultaneously. It provides real-time feedback on code changes, focusing on preserving existing code and avoiding unnecessary updates.Code Documentation Assistant: A tool that helps developers write better documentation without summaries or unnecessary confirmations. It guides users to provide instructions and documentation that are clear and evidence-backed.Error-Free Coding Mentor: An educational service that trains developers in best coding practices by providing real-time feedback following the provided rules. It discourages bad habits such as guessing, unnecessary confirmations, and whitespace suggestions.Code Consistency Checker: A tool that scans codebases to ensure consistency and adherence to coding standards, such as no apologies or confirmation requests in code comments and using single chunk edits for clarity.Version Control Enhancer: A version control system add-on that highlights any deviations from the coding rules during commit processes and encourages adherence before changes are pushed to repositories.Quality Assurance Plugin: A plugin for IDEs that offers quality checks aligned with the listed rules. It provides suggestions to maintain code integrity and development efficiency without unnecessary changes or inventions.Real File Linker: A service that automates linking code documentation to the actual files instead of placeholders like x.md, ensuring that developers have access to accurate resources.

## Benefits


## Synopsis
Developers working on code review tools could use this prompt to build a tool that automatically provides feedback on code adherence to specified guidelines.

## Overview of .cursorrules prompt
The .cursorrules file outlines a set of customizable rules designed to guide developers when making changes to code or documentation. Each rule specifies a pattern using regex to match specific words or phrases, and provides a message to advise or remind the developer of best practices. The rules cover various aspects such as verifying information, avoiding unnecessary comments or confirmations, preserving existing code, and ensuring edits are concise. The file aims to maintain code quality, clarity, and efficiency by enforcing these guidelines during the development and documentation process.

