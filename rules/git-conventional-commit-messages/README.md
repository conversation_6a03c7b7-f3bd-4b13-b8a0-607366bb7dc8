## Conventional Commit Messages

This is a .cursorrules prompt file for generating conventional commit messages.

## How to use

1. Copy the .cursorrules file to your project.
2. Open Cursor AI and select the .cursorrules file.
3. Start typing your commit message.
4. Cursor AI will generate a conventional commit message.
5. Copy the conventional commit message and paste it into your terminal.

Based on V1.0.0 of the Conventional Commit Messages specification: https://www.conventionalcommits.org/en/v1.0.0/#specification

