# Gherkin Style Testing Prompt

A specialized .cursorrules prompt for creating clear, structured test documentation in Gherkin format that non-technical stakeholders can understand.

## What You Can Build

- **Gherkin Test Scenarios**: Structured test documentation using Given-When-Then format
- **Feature Documentation**: Comprehensive test coverage for application features
- **Legal-Friendly Test Cases**: Test scenarios that legal and compliance teams can review
- **Human-Readable Test Plans**: Technical test cases converted into accessible language
- **Data-Driven Test Scenarios**: Parameterized tests using Scenario Outline and Examples tables

## Benefits

- **Cross-Team Collaboration**: Test documentation understandable by both technical and non-technical teams
- **Legal Compliance**: Structured format suitable for legal and regulatory documentation
- **User-Focused Testing**: Scenarios written from the user's perspective
- **Clear Test Requirements**: Well-defined expectations for test execution and outcomes
- **Organized Test Structure**: Consistent format with feature, scenario, and step definitions
- **Behavior-Driven Development**: Support for BDD practices in testing workflows

## Synopsis

This prompt helps QA engineers create high-quality Gherkin-format test documentation that bridges the gap between technical testing and business requirements, making test scenarios accessible to all stakeholders.

## Overview of .cursorrules Prompt

The .cursorrules prompt guides QA engineers in creating effective Gherkin documentation with these key elements:

- **Gherkin Syntax**: Guidelines for using Feature, Scenario, Given, When, Then, And, But effectively
- **Best Practices**: Eight essential practices for writing clear and effective Gherkin documentation
- **Example Structure**: Detailed example of a complete feature with background, scenarios, and notes
- **Technical Conversion**: Step-by-step process for converting technical test scripts to Gherkin format
- **Simple Language**: Emphasis on using non-technical language accessible to all stakeholders
- **Data Examples**: Techniques for incorporating data-driven testing with Examples tables
- **Scenario Organization**: Approaches for structuring related test scenarios within features
