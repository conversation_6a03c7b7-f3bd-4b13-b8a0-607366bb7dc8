---
description: Provides additional instructions for implementing HTMX with Go, including semantic HTML, CSRF protection, and error handling.
globs: **/*.html
---
- Use semantic HTML5 elements with HTMX attributes
- Implement proper CSRF protection
- Utilize HTMX extensions when needed
- Use hx-boost for full page navigation
- Follow Go's idiomatic error handling
- Implement graceful shutdown for the server
- Use Go modules for dependency management