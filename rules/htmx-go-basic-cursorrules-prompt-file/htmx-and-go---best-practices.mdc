---
description: Applies general best practices for using HTMX with Go, focusing on server-side rendering and request handling.
globs: **/*.go
---
- Use html/template for server-side rendering
- Implement http.HandlerFunc for handling HTMX requests
- Utilize gorilla/mux for routing if needed
- Use encoding/json for JSON responses
- Implement proper error handling and logging
- Utilize context for request cancellation and timeouts