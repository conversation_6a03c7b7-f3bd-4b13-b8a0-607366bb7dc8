# Cypress End-to-End Testing .cursorrules prompt file

Author: <PERSON>

## What you can build

End-to-End Test Suite: Create a comprehensive end-to-end test suite for web applications that validates critical user flows such as login, registration, checkout, and other key interactions. The tests focus on validating navigation paths, state updates, and error handling scenarios to ensure application reliability.Test Automation Framework: Develop a robust testing framework using Cypress that leverages best practices like data-testid selectors, API mocking with cy.intercept, and proper waiting strategies. This framework improves test reliability and maintainability while reducing flaky tests.Behavior-Driven Testing Solution: Implement tests that align with BDD practices by focusing on application behaviors from the user's perspective. Tests validate both success paths and error scenarios, providing comprehensive coverage of application functionality.TypeScript-Enhanced Test Suite: Build test suites that automatically detect and adapt to TypeScript projects, leveraging type safety and enhanced IDE features for more reliable test code and improved developer experience.CI/CD Testing Pipeline: Create a testing workflow that integrates seamlessly with CI/CD pipelines, providing rapid feedback on application quality with automated test runs for each build or deployment.

## Benefits

Reliability First Approach: Emphasizes practices that lead to stable, deterministic tests by using proper selectors, API mocking, and waiting strategies to reduce flakiness.TypeScript Auto-Detection: Automatically identifies TypeScript projects and adjusts test code syntax accordingly, enabling type safety without manual configuration.Best Practices Focus: Incorporates Cypress best practices like descriptive test naming, focused test files, and avoiding hardcoded waits for improved maintainability.End-to-End Flow Validation: Prioritizes testing complete user flows rather than isolated functionality, ensuring the application works correctly from the user's perspective.

## Synopsis

This prompt empowers web developers to create reliable, maintainable end-to-end test suites for their applications using Cypress, focusing on critical user flows and behavior validation.

## Overview of .cursorrules prompt

The .cursorrules file provides guidance for QA engineers and developers creating end-to-end UI tests with Cypress. It takes a TypeScript-aware approach, automatically detecting and adapting to TypeScript projects when present. The prompt focuses exclusively on end-to-end testing, emphasizing critical user flows and proper test structure. It promotes best practices like using data-testid selectors, implementing proper waiting strategies, mocking external dependencies, and creating focused test files with 3-5 tests each. The prompt includes a comprehensive example of a login test that demonstrates proper setup, API mocking, interaction patterns, and assertions for both success and error scenarios. Tests created with this prompt validate navigation paths, state updates, and error handling to ensure reliable applications.
